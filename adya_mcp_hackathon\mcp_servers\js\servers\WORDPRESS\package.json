{"name": "prathammanocha-comprehensive-wp-mcp", "version": "1.0.0", "description": "WordPress MCP Server with comprehensive API support", "type": "module", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "tsc -w"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.3", "axios": "^1.8.4", "openai": "^4.98.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^20.17.30", "typescript": "^5.0.0"}, "keywords": ["wordpress", "mcp", "api", "ai", "assistant"], "author": "", "license": "ISC"}