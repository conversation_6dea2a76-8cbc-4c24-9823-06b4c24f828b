ClientsConfig =[
    "MCP_CLIENT_AZURE_AI",
    "MCP_CLIENT_OPENAI",
	"MCP_CLIENT_GEMINI"
]
ServersConfig = [
	{
		"server_name": "MCP-GSUITE",
		"command":"uv",
		"args": [
			"--directory",
			"../servers/MCP-GSUITE/mcp-gsuite",
			"run",
			"mcp-gsuite"
		]
	},
	{
		"server_name": "MCP-JOOMLA",
		"command":"uv",
		"args": [
			"--directory",
			"../servers/MCP-JOOMLA/mcp-joomla",
			"run",
			"mcp-joomla"
		]
	},
	{
		"server_name": "CLI-MCP-SERVER",
		"command": "wsl",
		"args": [
			"-d",
			"FedoraLinux-42",
			"bash",
			"-c",
			"cd ~/cli-mcp-server && ALLOWED_DIR=/home/<USER>/mcp_allowed_dir ALLOWED_COMMANDS=ls,cat,pwd,echo,find,grep,stat,mkdir,df,free,uname,head,tail,sed ALLOWED_FLAGS=-l,-a,--help,--version,-h,-r,-n,-p,-maxdepth,-name,-type,-f MAX_COMMAND_LENGTH=2048 COMMAND_TIMEOUT=60 ALLOW_SHELL_OPERATORS=true ~/.local/bin/uv run cli-mcp-server"
		]
	}
]