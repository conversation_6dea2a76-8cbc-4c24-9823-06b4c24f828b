ClientsConfig =[
    "MCP_CLIENT_AZURE_AI",
    "MCP_CLIENT_OPENAI",
	"MCP_CLIENT_GEMINI"
]
ServersConfig = [
	{
		"server_name": "MCP-GSUITE",
		"command":"uv",
		"args": [
			"--directory",
			"../servers/MCP-GSUITE/mcp-gsuite",
			"run",
			"mcp-gsuite"
		]
	},
	{
		"server_name": "CLI-MCP-SERVER",
		"command": "wsl",
		"args": [
			"-d",
			"FedoraLinux-42",
			"bash",
			"-c",
			"cd ~/cli-mcp-server && ALLOWED_DIR=/home/<USER>/mcp_allowed_dir ALLOWED_COMMANDS=ls,cat,pwd,echo ALLOWED_FLAGS=-l,-a,--help,--version MAX_COMMAND_LENGTH=1024 COMMAND_TIMEOUT=30 ALLOW_SHELL_OPERATORS=false ~/.local/bin/uv run cli-mcp-server"
		]
	}
]