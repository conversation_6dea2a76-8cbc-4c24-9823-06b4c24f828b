[project]
name = "mcp-joomla"
version = "0.1.0"
description = "MCP server for Joomla CMS integration with comprehensive article and category management"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.1.0",
    "requests>=2.32.3",
    "httpx>=0.27.0",
    "markdown>=3.4.0",
    "bleach>=6.0.0"
]
authors = [
    { name = "Adya Hackathon Team", email = "<EMAIL>" },
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
mcp-joomla = "mcp_joomla:main"

[project.urls]
Homepage = "https://github.com/adya/mcp-joomla"
Documentation = "https://github.com/adya/mcp-joomla#readme"
Repository = "https://github.com/adya/mcp-joomla.git"
"Bug Tracker" = "https://github.com/adya/mcp-joomla/issues"
