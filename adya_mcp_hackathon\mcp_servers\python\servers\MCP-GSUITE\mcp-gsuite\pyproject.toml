[project]
name = "mcp-gsuite"
version = "0.3.0"
description = "Example MCP server to create a knowledge-base"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
 "beautifulsoup4>=4.12.3",
 "google-api-python-client>=2.154.0",
 "httplib2>=0.22.0",
 "mcp>=1.1.0",
 "python-dotenv>=1.0.1",
 "pytz>=2024.2",
 "requests>=2.32.3",
 "google-auth>=2.28.1",
 "google-auth-oauthlib>=1.2.0",
 "google-auth-httplib2>=0.2.0",
 "fastapi>=0.70.0",
]
[[project.authors]]
name = "<PERSON>"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "pyright>=1.1.389",
]

[project.scripts]
mcp-gsuite = "mcp_gsuite:main"
