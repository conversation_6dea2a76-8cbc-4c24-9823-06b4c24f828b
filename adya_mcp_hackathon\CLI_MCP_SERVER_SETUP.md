# CLI MCP Server WSL Integration Setup

This document provides step-by-step instructions for setting up and testing the CLI MCP Server integration with WSL FedoraLinux-42.

## Prerequisites

1. **WSL FedoraLinux-42** must be installed and configured
2. **UV package manager** must be installed in the WSL environment
3. **CLI MCP Server** must be cloned and set up in the WSL environment

## Setup Steps

### 1. WSL Environment Setup

```bash
# In WSL FedoraLinux-42 terminal
cd ~
git clone https://github.com/MladenSU/cli-mcp-server.git
cd cli-mcp-server

# Install dependencies
uv sync

# Create the allowed directory
mkdir -p /home/<USER>/mcp_allowed_dir
```

### 2. Test CLI MCP Server in WSL

```bash
# Test the server directly in WSL
cd ~/cli-mcp-server
export ALLOWED_DIR="/home/<USER>/mcp_allowed_dir"
export ALLOWED_COMMANDS="ls,cat,pwd,echo"
export ALLOWED_FLAGS="-l,-a,--help,--version"
export MAX_COMMAND_LENGTH="1024"
export COMMAND_TIMEOUT="30"
export ALLOW_SHELL_OPERATORS="false"

uv run cli-mcp-server
```

### 3. Test WSL Command from Windows

```powershell
# Test the WSL command from Windows PowerShell
wsl -d FedoraLinux-42 bash -c "cd ~/cli-mcp-server && uv run cli-mcp-server"
```

### 4. Configuration Files Updated

The following files have been updated to include the CLI MCP Server:

#### `client_and_server_config.py`
```python
{
    "server_name": "CLI-MCP-SERVER",
    "command": "wsl",
    "args": [
        "-d",
        "FedoraLinux-42", 
        "bash",
        "-c",
        "cd ~/cli-mcp-server && uv run cli-mcp-server"
    ],
    "env": {
        "ALLOWED_DIR": "/home/<USER>/mcp_allowed_dir",
        "ALLOWED_COMMANDS": "ls,cat,pwd,echo",
        "ALLOWED_FLAGS": "-l,-a,--help,--version",
        "MAX_COMMAND_LENGTH": "1024",
        "COMMAND_TIMEOUT": "30",
        "ALLOW_SHELL_OPERATORS": "false"
    }
}
```

#### `server_connection.py`
- Added support for environment variables in `StdioServerParameters`
- Environment variables are merged with the current environment
- Debug logging shows environment variables being passed

## Testing the Integration

### 1. Start the Python MCP Client

```bash
cd adya_mcp_hackathon/mcp_servers/python/clients
python run.py
```

### 2. Expected Output

You should see:
```
================= Initializing CLI-MCP-SERVER mcp server start ===============
Server name        : CLI-MCP-SERVER
Server command     : wsl
Server args        : ['-d', 'FedoraLinux-42', 'bash', '-c', 'cd ~/cli-mcp-server && uv run cli-mcp-server']
Environment vars   : {'ALLOWED_DIR': '/home/<USER>/mcp_allowed_dir', 'ALLOWED_COMMANDS': 'ls,cat,pwd,echo', ...}
Connected to CLI-MCP-SERVER with tools: ['run_command']
================= Initializing CLI-MCP-SERVER mcp server end ===============
```

### 3. Test API Endpoint

```bash
curl -X POST http://localhost:5000/api/v1/mcp/process_message \
  -H "Content-Type: application/json" \
  -d '{
    "selected_servers": ["CLI-MCP-SERVER"],
    "client_details": {
      "input": "List files in the allowed directory"
    },
    "selected_client": "MCP_CLIENT_OPENAI"
  }'
```

## Troubleshooting

### Common Issues

1. **WSL Distribution Not Found**
   ```
   Error: The specified distribution 'FedoraLinux-42' does not exist
   ```
   - Verify WSL distribution name: `wsl -l -v`
   - Update the distribution name in the configuration

2. **UV Not Found in WSL**
   ```
   bash: uv: command not found
   ```
   - Install UV in WSL: `curl -LsSf https://astral.sh/uv/install.sh | sh`

3. **Directory Not Found**
   ```
   ValueError: Valid ALLOWED_DIR is required
   ```
   - Create the directory: `mkdir -p /home/<USER>/mcp_allowed_dir`

4. **Permission Issues**
   ```
   Permission denied
   ```
   - Check directory permissions: `chmod 755 /home/<USER>/mcp_allowed_dir`

### Debug Commands

```bash
# Check WSL distributions
wsl -l -v

# Test WSL command execution
wsl -d FedoraLinux-42 bash -c "echo 'WSL is working'"

# Check if UV is installed in WSL
wsl -d FedoraLinux-42 bash -c "which uv"

# Check if cli-mcp-server directory exists
wsl -d FedoraLinux-42 bash -c "ls -la ~/cli-mcp-server"
```

## Security Considerations

- The CLI MCP Server is configured with strict security settings
- Only whitelisted commands are allowed: `ls,cat,pwd,echo`
- Only whitelisted flags are allowed: `-l,-a,--help,--version`
- Shell operators are disabled for security
- Command execution is restricted to the allowed directory
- Command timeout is set to 30 seconds
- Maximum command length is limited to 1024 characters

## Next Steps

After successful setup:
1. Test various CLI commands through the MCP interface
2. Integrate with LLM clients for automated command execution
3. Expand allowed commands as needed for your use case
4. Monitor logs for any security or performance issues
