Metadata-Version: 2.4
Name: mcp-joomla
Version: 0.1.0
Summary: MCP server for Joomla CMS integration with comprehensive article and category management
Project-URL: Homepage, https://github.com/adya/mcp-joomla
Project-URL: Documentation, https://github.com/adya/mcp-joomla#readme
Project-URL: Repository, https://github.com/adya/mcp-joomla.git
Project-URL: Bug Tracker, https://github.com/adya/mcp-joomla/issues
Author-email: <PERSON>ya Hackathon Team <<EMAIL>>
Requires-Python: >=3.10
Requires-Dist: bleach>=6.0.0
Requires-Dist: httpx>=0.27.0
Requires-Dist: markdown>=3.4.0
Requires-Dist: mcp>=1.1.0
Requires-Dist: requests>=2.32.3
Description-Content-Type: text/markdown

# MCP-JOOMLA Server

A Model Context Protocol (MCP) server for Joomla CMS integration.

## Features

- Article management (create, read, update, delete)
- Category management
- Article state management (publish, unpublish, archive, trash)
- Comprehensive Joomla API integration

## Installation

```bash
uv run mcp-joomla
```

## Usage

This server provides tools for managing Joomla content through the MCP protocol.

## Tools Available

- `get_articles` - Retrieve articles from Joomla
- `create_article` - Create new articles
- `get_categories` - Get article categories
- `manage_article_state` - Change article publication state
- `move_article_to_trash` - Move articles to trash
- `update_article` - Update existing articles

## Configuration

The server requires Joomla API credentials to be provided when calling tools:
- `base_url` - Your Joomla site URL
- `bearer_token` - API token for authentication
