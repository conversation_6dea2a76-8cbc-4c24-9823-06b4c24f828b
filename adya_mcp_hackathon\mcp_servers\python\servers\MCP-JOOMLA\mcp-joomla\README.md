# MCP-JOOMLA Server

A Model Context Protocol (MCP) server for Joomla CMS integration.

## Features

- **Article Management**: Create, read, update, search, and manage article states
- **Category Management**: Retrieve and work with Joomla categories
- **User Management**: Retrieve user information and details
- **Menu Management**: Access menu structures and navigation
- **Site Information**: Get general site configuration and details
- **Content Operations**: Move articles to trash, publish/unpublish content
- **Search Capabilities**: Search articles by title, content, and criteria
- **API Integration**: Full integration with Joomla's REST API
- **Security**: Bearer token authentication for secure API access

## Installation

```bash
uv run mcp-joomla
```

## Usage

This server provides tools for managing Joomla content through the MCP protocol.

## Tools Available

### Article Management (6 tools)
- `get_joomla_articles` - Retrieve all articles from Joomla
- `create_article` - Create new articles with content and metadata
- `get_article_by_id` - Retrieve a specific article by its ID
- `search_articles` - Search articles by title, content, or criteria
- `manage_article_state` - Change article publication state (publish/unpublish/archive/trash)
- `move_article_to_trash` - Move articles to trash with recovery option
- `update_article` - Update existing articles with new content

### Category Management (1 tool)
- `get_joomla_categories` - Retrieve all article categories

### User Management (2 tools)
- `get_joomla_users` - Retrieve all users with optional limit
- `get_user_by_id` - Get detailed information about a specific user

### Site Management (2 tools)
- `get_joomla_menus` - Retrieve menu structures and navigation items
- `get_site_info` - Get general site configuration and information

**Total: 12 tools available**

## Configuration

The server requires Joomla API credentials to be provided when calling tools:
- `base_url` - Your Joomla site URL
- `bearer_token` - API token for authentication
