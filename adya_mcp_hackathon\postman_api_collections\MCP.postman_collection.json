{"info": {"_postman_id": "638217d5-c664-445c-a953-7da2dcc41209", "name": "MCP", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "45403895"}, "item": [{"name": "MCP - Client APIs", "item": [{"name": "Gemini", "item": [{"name": "Non-Stream Api", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"WORDPRESS\": {\r\n            \"siteUrl\": \"\",\r\n            \"username\": \"\",\r\n            \"password\": \"\"\r\n        }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"max_token\":20000,\r\n        \"input\": \"use the credentials which user already provided ,on my G Drive, search for 'POA June 2025' google sheet and let me know whether available or not if available get the some data\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gemini-2.0-flash-lite\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_GEMINI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Non-Stream Api -python", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"MCP-GSUITE\": {\r\n            \"token\": \"\",\r\n            \"refresh_token\": \"\",\r\n            \"client_id\": \"\",\r\n            \"client_secret\": \"\"\r\n        }\r\n        // \"WORDPRESS\": {\r\n        //     \"siteUrl\": \"\",\r\n        //     \"username\": \"\",\r\n        //     \"password\": \"\"\r\n        // }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"max_token\": 20000,\r\n        \"input\": \"get the recent one email\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gemini-2.0-flash\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_GEMINI\",\r\n    \"selected_servers\": [\r\n        \"MCP-GSUITE\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-python-host}}/api/v1/mcp/process_message", "host": ["{{dev-python-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Stream API", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"WORDPRESS\": {\r\n            \"siteUrl\": \"\",\r\n            \"username\": \"\",\r\n            \"password\": \"\"\r\n        }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"max_token\":20000,\r\n        \"input\": \"get the recent published post\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gemini-2.0-flash-lite\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_GEMINI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message_stream", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message_stream"]}}, "response": []}]}, {"name": "Azure OpenAi", "item": [{"name": "Non-Stream Api", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"WORDPRESS\": {\r\n            \"siteUrl\": \"\",\r\n            \"username\": \"\",\r\n            \"password\": \"\"\r\n        }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"endpoint\": \"\",\r\n        \"deployment_id\": \"gpt-4o\",\r\n        \"api_version\": \"\",\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"create a category 'Grocery'\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_AZURE_AI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Non-Stream Api -python", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"WORDPRESS\": {\r\n            \"siteUrl\": \"\",\r\n            \"username\": \"\",\r\n            \"password\": \"\"\r\n        }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"endpoint\": \"\",\r\n        \"deployment_id\": \"gpt-4o\",\r\n        \"api_version\": \"\",\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"get the recent once email\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_AZURE_AI\",\r\n    \"selected_servers\": [\r\n        \"MCP-GSUITE\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-python-host}}/api/v1/mcp/process_message", "host": ["{{dev-python-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Stream API", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n\r\n         \"G_DRIVE\": {\r\n            \"web\": {\r\n                \"client_id\":\"\",\r\n                \"client_secret\":\"\",\r\n                \"refresh_token\": \"\",\r\n                \"access_token\": \"\"\r\n            }\r\n        }\r\n    },\r\n     \"client_details\": {\r\n        \"endpoint\": \"\",\r\n        \"deployment_id\": \"gpt-4o\",\r\n        \"api_version\": \"\",\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"get the recent once email\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_AZURE_AI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message_stream", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message_stream"]}}, "response": []}]}, {"name": "Openai", "item": [{"name": "Non-Stream Api", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n\r\n         \"G_DRIVE\": {\r\n            \"web\": {\r\n                \"client_id\":\"\",\r\n                \"client_secret\":\"\",\r\n                \"refresh_token\": \"\",\r\n                \"access_token\": \"\"\r\n            }\r\n        }\r\n    },\r\n    \"client_details\": {\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"Hii\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_OPENAI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Non-Stream Api -python", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"MCP-GSUITE\": {\r\n            \"token\": \"\",\r\n            \"refresh_token\": \"\",\r\n            \"client_id\": \"\",\r\n            \"client_secret\": \"\"\r\n        }\r\n        // \"WORDPRESS\": {\r\n        //     \"siteUrl\": \"\",\r\n        //     \"username\": \"\",\r\n        //     \"password\": \"\"\r\n        // }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\":{\r\n        \"api_key\": \"XkSQ0wdBTaZkxlvEEbQBxOAlPK1LhNL_XrcVtgA\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"get the recent email\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_OPENAI\",\r\n    \"selected_servers\": [\r\n        \"MCP-GSUITE\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-python-host}}/api/v1/mcp/process_message", "host": ["{{dev-python-host}}"], "path": ["api", "v1", "mcp", "process_message"]}}, "response": []}, {"name": "Stream API", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"selected_server_credentials\": {\r\n        \"WORDPRESS\": {\r\n            \"siteUrl\": \"\",\r\n            \"username\": \"\",\r\n            \"password\": \"\"\r\n        }\r\n        //  \"G_DRIVE\": {\r\n        //     \"web\": {\r\n        //         \"client_id\":\"\",\r\n        //         \"client_secret\":\"\",\r\n        //         \"refresh_token\": \"\",\r\n        //         \"access_token\": \"\"\r\n        //     }\r\n        // }\r\n    },\r\n    \"client_details\": {\r\n        \"api_key\": \"\",\r\n        \"temperature\": 0.1,\r\n        \"input\": \"Hii\",\r\n        \"input_type\": \"text\",\r\n        \"prompt\": \"you are a helpful assistant\",\r\n        \"chat_model\": \"gpt-4o\",\r\n        \"chat_history\": [\r\n            {\r\n                \"role\": \"user\",\r\n                \"content\": \"Hii\"\r\n            }\r\n        ]\r\n    },\r\n    \"selected_client\": \"MCP_CLIENT_OPENAI\",\r\n    \"selected_servers\": [\r\n        \"WORDPRESS\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{dev-js-host}}/api/v1/mcp/process_message_stream", "host": ["{{dev-js-host}}"], "path": ["api", "v1", "mcp", "process_message_stream"]}}, "response": []}]}]}]}