swagger: "2.0"
info:
  title: Calendar
  description: Manipulates events and other calendar data.
  contact:
    name: Google
    url: https://google.com
  version: v3
host: www.googleapis.com
basePath: /calendar/v3
schemes:
- http
produces:
- application/json
consumes:
- application/json
paths:
  /calendars:
    post:
      summary: Create Calendar
      description: Creates a secondary calendar
      operationId: calendar.calendars.insert
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      responses:
        200:
          description: OK
      tags:
      - Calendar
  /calendars/{calendarId}:
    delete:
      summary: CreaDeletete Calendar
      description: Deletes a secondary calendar
      operationId: calendar.calendars.delete
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar
    get:
      summary: Get Calendar
      description: Returns metadata for a calendar
      operationId: calendar.calendars.get
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar
    patch:
      summary: Update Calendar
      description: Updates metadata for a calendar
      operationId: calendar.calendars.patch
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar
    put:
      summary: Update Calendar
      description: Updates metadata for a calendar
      operationId: calendar.calendars.update
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar
  /calendars/{calendarId}/acl:
    get:
      summary: Get Calendar ACL
      description: Returns the rules in the access control list for the calendar
      operationId: calendar.acl.list
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: showDeleted
        description: Whether to include deleted ACLs in the result
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
    post:
      summary: Create Calendar ACL
      description: Creates an access control rule
      operationId: calendar.acl.insert
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
  /calendars/{calendarId}/acl/watch:
    post:
      summary: Watch Calendar ACL
      description: Watch for changes to ACL resources
      operationId: calendar.acl.watch
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: body
        name: resource
        schema:
          $ref: '#/definitions/holder'
      - in: query
        name: showDeleted
        description: Whether to include deleted ACLs in the result
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
  /calendars/{calendarId}/acl/{ruleId}:
    delete:
      summary: Delete Calendar ACL
      description: Deletes an access control rule
      operationId: calendar.acl.delete
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: ruleId
        description: ACL rule identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
    get:
      summary: Get Calendar ACL
      description: Returns an access control rule
      operationId: calendar.acl.get
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: ruleId
        description: ACL rule identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
    patch:
      summary: Update Calendar ACL
      description: Updates an access control rule
      operationId: calendar.acl.patch
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: ruleId
        description: ACL rule identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
    put:
      summary: Update Calendar ACL
      description: Updates an access control rule
      operationId: calendar.acl.update
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: ruleId
        description: ACL rule identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar ACL
  /calendars/{calendarId}/clear:
    post:
      summary: Clear Primary Calendar
      description: Clears a primary calendar
      operationId: calendar.calendars.clear
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Calendar
  /calendars/{calendarId}/events:
    get:
      summary: Get Events
      description: Returns events on the specified calendar
      operationId: calendar.events.list
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: iCalUID
        description: Specifies event ID in the iCalendar format to be included in
          the response
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: maxResults
        description: Maximum number of events returned on one result page
      - in: query
        name: orderBy
        description: The order of the events returned in the result
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: privateExtendedProperty
        description: Extended properties constraint specified as propertyName=value
      - in: query
        name: q
        description: Free text search terms to find events that match these terms
          in any field, except for extended properties
      - in: query
        name: sharedExtendedProperty
        description: Extended properties constraint specified as propertyName=value
      - in: query
        name: showDeleted
        description: Whether to include deleted events (with status equals "cancelled")
          in the result
      - in: query
        name: showHiddenInvitations
        description: Whether to include hidden invitations in the result
      - in: query
        name: singleEvents
        description: Whether to expand recurring events into instances and only return
          single one-off events and instances of recurring events, but not the underlying
          recurring events themselves
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      - in: query
        name: timeMax
        description: Upper bound (exclusive) for an event's start time to filter by
      - in: query
        name: timeMin
        description: Lower bound (inclusive) for an event's end time to filter by
      - in: query
        name: timeZone
        description: Time zone used in the response
      - in: query
        name: updatedMin
        description: Lower bound for an event's last modification time (as a RFC3339
          timestamp) to filter by
      responses:
        200:
          description: OK
      tags:
      - Event
    post:
      summary: Create Event
      description: Creates an event
      operationId: calendar.events.insert
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the creation of the new event
      - in: query
        name: supportsAttachments
        description: Whether API client performing operation supports event attachments
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/import:
    post:
      summary: Import Event
      description: Imports an event
      operationId: calendar.events.import
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: supportsAttachments
        description: Whether API client performing operation supports event attachments
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/quickAdd:
    post:
      summary: Create Event
      description: Creates an event based on a simple text string
      operationId: calendar.events.quickAdd
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the creation of the event
      - in: query
        name: text
        description: The text describing the event to be created
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/watch:
    post:
      summary: Watch Event
      description: Watch for changes to Events resources
      operationId: calendar.events.watch
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: iCalUID
        description: Specifies event ID in the iCalendar format to be included in
          the response
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: maxResults
        description: Maximum number of events returned on one result page
      - in: query
        name: orderBy
        description: The order of the events returned in the result
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: privateExtendedProperty
        description: Extended properties constraint specified as propertyName=value
      - in: query
        name: q
        description: Free text search terms to find events that match these terms
          in any field, except for extended properties
      - in: body
        name: resource
        schema:
          $ref: '#/definitions/holder'
      - in: query
        name: sharedExtendedProperty
        description: Extended properties constraint specified as propertyName=value
      - in: query
        name: showDeleted
        description: Whether to include deleted events (with status equals "cancelled")
          in the result
      - in: query
        name: showHiddenInvitations
        description: Whether to include hidden invitations in the result
      - in: query
        name: singleEvents
        description: Whether to expand recurring events into instances and only return
          single one-off events and instances of recurring events, but not the underlying
          recurring events themselves
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      - in: query
        name: timeMax
        description: Upper bound (exclusive) for an event's start time to filter by
      - in: query
        name: timeMin
        description: Lower bound (inclusive) for an event's end time to filter by
      - in: query
        name: timeZone
        description: Time zone used in the response
      - in: query
        name: updatedMin
        description: Lower bound for an event's last modification time (as a RFC3339
          timestamp) to filter by
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/{eventId}:
    delete:
      summary: Delete Event
      description: Deletes an event
      operationId: calendar.events.delete
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: eventId
        description: Event identifier
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the deletion of the event
      responses:
        200:
          description: OK
      tags:
      - Event
    get:
      summary: Get Event
      description: Returns an event
      operationId: calendar.events.get
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: eventId
        description: Event identifier
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: timeZone
        description: Time zone used in the response
      responses:
        200:
          description: OK
      tags:
      - Event
    patch:
      summary: Update Event
      description: Updates an event
      operationId: calendar.events.patch
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: eventId
        description: Event identifier
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the event update (e
      - in: query
        name: supportsAttachments
        description: Whether API client performing operation supports event attachments
      responses:
        200:
          description: OK
      tags:
      - Event
    put:
      summary: Update Event
      description: Updates an event
      operationId: calendar.events.update
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: eventId
        description: Event identifier
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the event update (e
      - in: query
        name: supportsAttachments
        description: Whether API client performing operation supports event attachments
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/{eventId}/instances:
    get:
      summary: Get Event Instance
      description: Returns instances of the specified recurring event
      operationId: calendar.events.instances
      parameters:
      - in: query
        name: alwaysIncludeEmail
        description: Whether to always include a value in the email field for the
          organizer, creator and attendees, even if no real email is available (i
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: path
        name: eventId
        description: Recurring event identifier
      - in: query
        name: maxAttendees
        description: The maximum number of attendees to include in the response
      - in: query
        name: maxResults
        description: Maximum number of events returned on one result page
      - in: query
        name: originalStart
        description: The original start time of the instance in the result
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: showDeleted
        description: Whether to include deleted events (with status equals "cancelled")
          in the result
      - in: query
        name: timeMax
        description: Upper bound (exclusive) for an event's start time to filter by
      - in: query
        name: timeMin
        description: Lower bound (inclusive) for an event's end time to filter by
      - in: query
        name: timeZone
        description: Time zone used in the response
      responses:
        200:
          description: OK
      tags:
      - Event
  /calendars/{calendarId}/events/{eventId}/move:
    post:
      summary: Move Event
      description: Moves an event to another calendar, i
      operationId: calendar.events.move
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier of the source calendar where the event currently
          is on
      - in: query
        name: destination
        description: Calendar identifier of the target calendar where the event is
          to be moved to
      - in: path
        name: eventId
        description: Event identifier
      - in: query
        name: sendNotifications
        description: Whether to send notifications about the change of the event's
          organizer
      responses:
        200:
          description: OK
      tags:
      - Event
  /channels/stop:
    post:
      summary: Stop Watching Resource
      description: Stop watching resources through this channel
      operationId: calendar.channels.stop
      parameters:
      - in: body
        name: resource
        schema:
          $ref: '#/definitions/holder'
      responses:
        200:
          description: OK
      tags:
      - Watch
  /colors:
    get:
      summary: Get Colors
      description: Returns the color definitions for calendars and events
      operationId: calendar.colors.get
      responses:
        200:
          description: OK
      tags:
      - Color
  /freeBusy:
    post:
      summary: Return Free/Busy Information
      description: Returns free/busy information for a set of calendars
      operationId: calendar.freebusy.query
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      responses:
        200:
          description: OK
      tags:
      - Free/Busy
  /users/me/calendarList:
    get:
      summary: Return Entries
      description: Returns entries on the user's calendar list
      operationId: calendar.calendarList.list
      parameters:
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: minAccessRole
        description: The minimum access role for the user in the returned entries
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: showDeleted
        description: Whether to include deleted calendar list entries in the result
      - in: query
        name: showHidden
        description: Whether to show hidden entries
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Event
    post:
      summary: Add Entry
      description: Adds an entry to the user's calendar list
      operationId: calendar.calendarList.insert
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: query
        name: colorRgbFormat
        description: Whether to use the foregroundColor and backgroundColor fields
          to write the calendar colors (RGB)
      responses:
        200:
          description: OK
      tags:
      - Event
  /users/me/calendarList/watch:
    post:
      summary: Watch Entry
      description: Watch for changes to CalendarList resources
      operationId: calendar.calendarList.watch
      parameters:
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: minAccessRole
        description: The minimum access role for the user in the returned entries
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: body
        name: resource
        schema:
          $ref: '#/definitions/holder'
      - in: query
        name: showDeleted
        description: Whether to include deleted calendar list entries in the result
      - in: query
        name: showHidden
        description: Whether to show hidden entries
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Event
  /users/me/calendarList/{calendarId}:
    delete:
      summary: Delete Entry
      description: Deletes an entry on the user's calendar list
      operationId: calendar.calendarList.delete
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Event
    get:
      summary: Get Entry
      description: Returns an entry on the user's calendar list
      operationId: calendar.calendarList.get
      parameters:
      - in: path
        name: calendarId
        description: Calendar identifier
      responses:
        200:
          description: OK
      tags:
      - Event
    patch:
      summary: Update Entry
      description: Updates an entry on the user's calendar list
      operationId: calendar.calendarList.patch
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: colorRgbFormat
        description: Whether to use the foregroundColor and backgroundColor fields
          to write the calendar colors (RGB)
      responses:
        200:
          description: OK
      tags:
      - Event
    put:
      summary: Update Entry
      description: Updates an entry on the user's calendar list
      operationId: calendar.calendarList.update
      parameters:
      - in: body
        name: body
        schema:
          $ref: '#/definitions/holder'
      - in: path
        name: calendarId
        description: Calendar identifier
      - in: query
        name: colorRgbFormat
        description: Whether to use the foregroundColor and backgroundColor fields
          to write the calendar colors (RGB)
      responses:
        200:
          description: OK
      tags:
      - Event
  /users/me/settings:
    get:
      summary: Get Settings
      description: Returns all user settings for the authenticated user
      operationId: calendar.settings.list
      parameters:
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Setting
  /users/me/settings/watch:
    post:
      summary: Watch Settings
      description: Watch for changes to Settings resources
      operationId: calendar.settings.watch
      parameters:
      - in: query
        name: maxResults
        description: Maximum number of entries returned on one result page
      - in: query
        name: pageToken
        description: Token specifying which result page to return
      - in: body
        name: resource
        schema:
          $ref: '#/definitions/holder'
      - in: query
        name: syncToken
        description: Token obtained from the nextSyncToken field returned on the last
          page of results from the previous list request
      responses:
        200:
          description: OK
      tags:
      - Setting
  /users/me/settings/{setting}:
    get:
      summary: Get Setting
      description: Returns a single user setting
      operationId: calendar.settings.get
      parameters:
      - in: path
        name: setting
        description: The id of the user setting
      responses:
        200:
          description: OK
      tags:
      - Setting
definitions:
  Acl:
    properties:
      etag:
        description: This is a default description.
        type: parameters
      items:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      nextPageToken:
        description: This is a default description.
        type: parameters
      nextSyncToken:
        description: This is a default description.
        type: parameters
  AclRule:
    properties:
      etag:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      role:
        description: This is a default description.
        type: parameters
      scope:
        description: This is a default description.
        type: parameters
  Calendar:
    properties:
      description:
        description: This is a default description.
        type: parameters
      etag:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      location:
        description: This is a default description.
        type: parameters
      summary:
        description: This is a default description.
        type: parameters
      timeZone:
        description: This is a default description.
        type: parameters
  CalendarList:
    properties:
      etag:
        description: This is a default description.
        type: parameters
      items:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      nextPageToken:
        description: This is a default description.
        type: parameters
      nextSyncToken:
        description: This is a default description.
        type: parameters
  CalendarListEntry:
    properties:
      accessRole:
        description: This is a default description.
        type: parameters
      backgroundColor:
        description: This is a default description.
        type: parameters
      colorId:
        description: This is a default description.
        type: parameters
      defaultReminders:
        description: This is a default description.
        type: parameters
      deleted:
        description: This is a default description.
        type: parameters
      description:
        description: This is a default description.
        type: parameters
      etag:
        description: This is a default description.
        type: parameters
      foregroundColor:
        description: This is a default description.
        type: parameters
      hidden:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      location:
        description: This is a default description.
        type: parameters
      notificationSettings:
        description: This is a default description.
        type: parameters
      primary:
        description: This is a default description.
        type: parameters
      selected:
        description: This is a default description.
        type: parameters
      summary:
        description: This is a default description.
        type: parameters
      summaryOverride:
        description: This is a default description.
        type: parameters
      timeZone:
        description: This is a default description.
        type: parameters
  CalendarNotification:
    properties:
      method:
        description: This is a default description.
        type: parameters
      type:
        description: This is a default description.
        type: parameters
  Channel:
    properties:
      address:
        description: This is a default description.
        type: parameters
      expiration:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      params:
        description: This is a default description.
        type: parameters
      payload:
        description: This is a default description.
        type: parameters
      resourceId:
        description: This is a default description.
        type: parameters
      resourceUri:
        description: This is a default description.
        type: parameters
      token:
        description: This is a default description.
        type: parameters
      type:
        description: This is a default description.
        type: parameters
  ColorDefinition:
    properties:
      background:
        description: This is a default description.
        type: parameters
      foreground:
        description: This is a default description.
        type: parameters
  Colors:
    properties:
      calendar:
        description: This is a default description.
        type: parameters
      event:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      updated:
        description: This is a default description.
        type: parameters
  Error:
    properties:
      domain:
        description: This is a default description.
        type: parameters
      reason:
        description: This is a default description.
        type: parameters
  Event:
    properties:
      anyoneCanAddSelf:
        description: This is a default description.
        type: parameters
      attachments:
        description: This is a default description.
        type: parameters
      attendees:
        description: This is a default description.
        type: parameters
      attendeesOmitted:
        description: This is a default description.
        type: parameters
      colorId:
        description: This is a default description.
        type: parameters
      created:
        description: This is a default description.
        type: parameters
      creator:
        description: This is a default description.
        type: parameters
      description:
        description: This is a default description.
        type: parameters
      endTimeUnspecified:
        description: This is a default description.
        type: parameters
      etag:
        description: This is a default description.
        type: parameters
      extendedProperties:
        description: This is a default description.
        type: parameters
      gadget:
        description: This is a default description.
        type: parameters
      guestsCanInviteOthers:
        description: This is a default description.
        type: parameters
      guestsCanModify:
        description: This is a default description.
        type: parameters
      guestsCanSeeOtherGuests:
        description: This is a default description.
        type: parameters
      hangoutLink:
        description: This is a default description.
        type: parameters
      htmlLink:
        description: This is a default description.
        type: parameters
      iCalUID:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      location:
        description: This is a default description.
        type: parameters
      locked:
        description: This is a default description.
        type: parameters
      organizer:
        description: This is a default description.
        type: parameters
      privateCopy:
        description: This is a default description.
        type: parameters
      recurrence:
        description: This is a default description.
        type: parameters
      recurringEventId:
        description: This is a default description.
        type: parameters
      reminders:
        description: This is a default description.
        type: parameters
      sequence:
        description: This is a default description.
        type: parameters
      source:
        description: This is a default description.
        type: parameters
      status:
        description: This is a default description.
        type: parameters
      summary:
        description: This is a default description.
        type: parameters
      transparency:
        description: This is a default description.
        type: parameters
      updated:
        description: This is a default description.
        type: parameters
      visibility:
        description: This is a default description.
        type: parameters
  EventAttachment:
    properties:
      fileId:
        description: This is a default description.
        type: parameters
      fileUrl:
        description: This is a default description.
        type: parameters
      iconLink:
        description: This is a default description.
        type: parameters
      mimeType:
        description: This is a default description.
        type: parameters
      title:
        description: This is a default description.
        type: parameters
  EventAttendee:
    properties:
      additionalGuests:
        description: This is a default description.
        type: parameters
      comment:
        description: This is a default description.
        type: parameters
      displayName:
        description: This is a default description.
        type: parameters
      email:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      optional:
        description: This is a default description.
        type: parameters
      organizer:
        description: This is a default description.
        type: parameters
      resource:
        description: This is a default description.
        type: parameters
      responseStatus:
        description: This is a default description.
        type: parameters
      self:
        description: This is a default description.
        type: parameters
  EventDateTime:
    properties:
      date:
        description: This is a default description.
        type: parameters
      dateTime:
        description: This is a default description.
        type: parameters
      timeZone:
        description: This is a default description.
        type: parameters
  EventReminder:
    properties:
      method:
        description: This is a default description.
        type: parameters
      minutes:
        description: This is a default description.
        type: parameters
  Events:
    properties:
      accessRole:
        description: This is a default description.
        type: parameters
      defaultReminders:
        description: This is a default description.
        type: parameters
      description:
        description: This is a default description.
        type: parameters
      etag:
        description: This is a default description.
        type: parameters
      items:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      nextPageToken:
        description: This is a default description.
        type: parameters
      nextSyncToken:
        description: This is a default description.
        type: parameters
      summary:
        description: This is a default description.
        type: parameters
      timeZone:
        description: This is a default description.
        type: parameters
      updated:
        description: This is a default description.
        type: parameters
  FreeBusyCalendar:
    properties:
      busy:
        description: This is a default description.
        type: parameters
      errors:
        description: This is a default description.
        type: parameters
  FreeBusyGroup:
    properties:
      calendars:
        description: This is a default description.
        type: parameters
      errors:
        description: This is a default description.
        type: parameters
  FreeBusyRequest:
    properties:
      calendarExpansionMax:
        description: This is a default description.
        type: parameters
      groupExpansionMax:
        description: This is a default description.
        type: parameters
      items:
        description: This is a default description.
        type: parameters
      timeMax:
        description: This is a default description.
        type: parameters
      timeMin:
        description: This is a default description.
        type: parameters
      timeZone:
        description: This is a default description.
        type: parameters
  FreeBusyRequestItem:
    properties:
      id:
        description: This is a default description.
        type: parameters
  FreeBusyResponse:
    properties:
      calendars:
        description: This is a default description.
        type: parameters
      groups:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      timeMax:
        description: This is a default description.
        type: parameters
      timeMin:
        description: This is a default description.
        type: parameters
  Setting:
    properties:
      etag:
        description: This is a default description.
        type: parameters
      id:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      value:
        description: This is a default description.
        type: parameters
  Settings:
    properties:
      etag:
        description: This is a default description.
        type: parameters
      items:
        description: This is a default description.
        type: parameters
      kind:
        description: This is a default description.
        type: parameters
      nextPageToken:
        description: This is a default description.
        type: parameters
      nextSyncToken:
        description: This is a default description.
        type: parameters
  TimePeriod:
    properties:
      end:
        description: This is a default description.
        type: parameters
      start:
        description: This is a default description.
        type: parameters